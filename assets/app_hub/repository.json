{"repository": {"name": "<PERSON><PERSON>pp Hub", "version": "1.0.0", "description": "Official repository for Raspberry Pi software packages", "last_updated": "2024-01-01T00:00:00Z"}, "categories": [{"id": "development", "name": "Development", "description": "Programming languages, IDEs, and development tools", "icon": "code"}, {"id": "media", "name": "Media & Entertainment", "description": "Media servers, streaming, and entertainment applications", "icon": "play_circle"}, {"id": "networking", "name": "Networking", "description": "Network tools, servers, and connectivity applications", "icon": "network_check"}, {"id": "system", "name": "System Tools", "description": "System utilities, monitoring, and administration tools", "icon": "settings"}, {"id": "iot", "name": "IoT & Automation", "description": "Internet of Things and home automation platforms", "icon": "home"}, {"id": "security", "name": "Security", "description": "Security tools, VPN, and privacy applications", "icon": "security"}], "applications": [{"id": "docker", "name": "<PERSON>er", "description": "Platform for developing, shipping, and running applications in containers", "version": "latest", "category": "development", "icon": "docker.png", "size": "~200MB", "install_type": "apt", "install_commands": ["curl -fsSL https://get.docker.com -o get-docker.sh", "sudo sh get-docker.sh", "sudo usermod -aG docker $USER", "rm get-docker.sh"], "remove_commands": ["sudo apt remove -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin", "sudo rm -rf /var/lib/docker", "sudo rm -rf /var/lib/containerd"], "check_installed": "docker --version", "tags": ["containers", "development", "virtualization"], "requires_reboot": false, "pi_only": false}, {"id": "nodejs", "name": "Node.js", "description": "JavaScript runtime built on Chrome's V8 JavaScript engine", "version": "20.x", "category": "development", "icon": "nodejs.png", "size": "~50MB", "install_type": "script", "install_commands": ["curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -", "sudo apt install -y nodejs"], "remove_commands": ["sudo apt remove -y nodejs npm", "sudo rm -f /etc/apt/sources.list.d/nodesource.list"], "check_installed": "node --version", "tags": ["javascript", "development", "runtime"], "requires_reboot": false, "pi_only": false}, {"id": "python3-pip", "name": "Python 3 & Pip", "description": "Python 3 programming language with package installer", "version": "3.x", "category": "development", "icon": "python.png", "size": "~30MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y python3 python3-pip python3-venv"], "remove_commands": ["sudo apt remove -y python3-pip python3-venv"], "check_installed": "python3 --version && pip3 --version", "tags": ["python", "development", "programming"], "requires_reboot": false, "pi_only": false}, {"id": "plex", "name": "Plex Media Server", "description": "Stream your media collection to any device, anywhere", "version": "latest", "category": "media", "icon": "plex.png", "size": "~150MB", "install_type": "deb", "install_commands": ["wget -O plex.deb https://downloads.plex.tv/plex-media-server-new/1.40.1.8227-c0dd5a73e/debian/plexmediaserver_1.40.1.8227-c0dd5a73e_arm64.deb", "sudo dpkg -i plex.deb", "sudo apt install -f -y", "rm plex.deb"], "remove_commands": ["sudo apt remove -y plexmediaserver"], "check_installed": "systemctl is-active plexmediaserver", "tags": ["media", "streaming", "server"], "requires_reboot": false, "pi_only": false}, {"id": "nginx", "name": "Nginx Web Server", "description": "High-performance HTTP server and reverse proxy", "version": "latest", "category": "networking", "icon": "nginx.png", "size": "~20MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y nginx", "sudo systemctl enable nginx", "sudo systemctl start nginx"], "remove_commands": ["sudo systemctl stop nginx", "sudo systemctl disable nginx", "sudo apt remove -y nginx nginx-common"], "check_installed": "nginx -v", "tags": ["web server", "http", "proxy"], "requires_reboot": false, "pi_only": false}, {"id": "htop", "name": "htop", "description": "Interactive process viewer and system monitor", "version": "latest", "category": "system", "icon": "htop.png", "size": "~1MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y htop"], "remove_commands": ["sudo apt remove -y htop"], "check_installed": "htop --version", "tags": ["monitoring", "system", "processes"], "requires_reboot": false, "pi_only": false}, {"id": "home-assistant", "name": "Home Assistant", "description": "Open source home automation platform", "version": "latest", "category": "iot", "icon": "homeassistant.png", "size": "~500MB", "install_type": "docker", "install_commands": ["docker run -d --name homeassistant --privileged --restart=unless-stopped -e TZ=America/New_York -v /home/<USER>/homeassistant:/config --network=host ghcr.io/home-assistant/home-assistant:stable"], "remove_commands": ["docker stop homeassistant", "docker rm homeassistant", "sudo rm -rf /home/<USER>/homeassistant"], "check_installed": "docker ps | grep homeassistant", "tags": ["automation", "iot", "smart home"], "requires_reboot": false, "pi_only": false, "dependencies": ["docker"]}, {"id": "pihole", "name": "Pi-hole", "description": "Network-wide ad blocker and DNS sinkhole", "version": "latest", "category": "networking", "icon": "pihole.png", "size": "~100MB", "install_type": "script", "install_commands": ["curl -sSL https://install.pi-hole.net | bash"], "remove_commands": ["pihole uninstall"], "check_installed": "pihole version", "tags": ["dns", "ad blocker", "network"], "requires_reboot": false, "pi_only": true}, {"id": "wireguard", "name": "<PERSON><PERSON><PERSON>N", "description": "Fast, modern, secure VPN tunnel", "version": "latest", "category": "security", "icon": "wireguard.png", "size": "~10MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y wireguard"], "remove_commands": ["sudo apt remove -y wireguard"], "check_installed": "wg --version", "tags": ["vpn", "security", "networking"], "requires_reboot": false, "pi_only": false}, {"id": "git", "name": "Git", "description": "Distributed version control system", "version": "latest", "category": "development", "icon": "git.png", "size": "~15MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y git"], "remove_commands": ["sudo apt remove -y git"], "check_installed": "git --version", "tags": ["version control", "development", "source code"], "requires_reboot": false, "pi_only": false}]}