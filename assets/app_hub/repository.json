{"repository": {"name": "<PERSON><PERSON>pp Hub", "version": "2.0.0", "description": "Comprehensive repository for Raspberry Pi software packages", "last_updated": "2024-12-19T00:00:00Z"}, "categories": [{"id": "appearance", "name": "Appearance", "description": "Themes, fonts, and visual customization tools", "icon": "palette"}, {"id": "creative", "name": "Creative Arts", "description": "Graphics, design, and creative applications", "icon": "brush"}, {"id": "engineering", "name": "Engineering", "description": "CAD, 3D printing, and engineering tools", "icon": "engineering"}, {"id": "games", "name": "Games", "description": "Gaming platforms, emulators, and entertainment", "icon": "games"}, {"id": "internet", "name": "Internet", "description": "Network tools, download managers, and web utilities", "icon": "public"}, {"id": "browsers", "name": "Internet/Browsers", "description": "Web browsers and browsing tools", "icon": "web"}, {"id": "communication", "name": "Internet/Communication", "description": "Chat, messaging, and communication applications", "icon": "chat"}, {"id": "multimedia", "name": "Multimedia", "description": "Audio, video, and media editing applications", "icon": "movie"}, {"id": "office", "name": "Office", "description": "Productivity, document editing, and office tools", "icon": "work"}, {"id": "programming", "name": "Programming", "description": "IDEs, development tools, and programming languages", "icon": "code"}, {"id": "system", "name": "System Management", "description": "System utilities, monitoring, and administration tools", "icon": "settings"}, {"id": "terminals", "name": "Terminals", "description": "Terminal emulators and command-line tools", "icon": "terminal"}, {"id": "tools", "name": "Tools", "description": "General utilities and productivity tools", "icon": "build"}, {"id": "crypto", "name": "Tools/Crypto", "description": "Cryptocurrency and blockchain tools", "icon": "currency_bitcoin"}, {"id": "emulation", "name": "Tools/Emulation", "description": "Emulation and virtualization tools", "icon": "computer"}], "applications": [{"id": "caskaydia-cove-nf", "name": "Caskaydia Cove NF", "description": "Nerd Font version of Cascadia Code with programming ligatures and symbols", "version": "latest", "category": "appearance", "icon": "font_download", "size": "~5MB", "install_type": "script", "install_commands": ["wget -O /tmp/CascadiaCode.zip https://github.com/ryanoasis/nerd-fonts/releases/latest/download/CascadiaCode.zip", "sudo unzip -o /tmp/CascadiaCode.zip -d /usr/share/fonts/truetype/", "sudo fc-cache -fv", "rm /tmp/CascadiaCode.zip"], "remove_commands": ["sudo rm -f /usr/share/fonts/truetype/CaskaydiaCove*.ttf", "sudo fc-cache -fv"], "check_installed": "fc-list | grep -i caskaydia", "check_version": "fc-list | grep -i caskaydia | head -1", "tags": ["font", "programming", "nerd-font"], "requires_reboot": false, "pi_only": false}, {"id": "colored-man-pages", "name": "Colored Man Pages", "description": "Add syntax highlighting and colors to manual pages for better readability", "version": "latest", "category": "appearance", "icon": "colorize", "size": "~1MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y most", "echo 'export PAGER=most' >> ~/.bashrc"], "remove_commands": ["sudo apt remove -y most", "sed -i '/export PAGER=most/d' ~/.bashrc"], "check_installed": "which most", "tags": ["terminal", "colors", "documentation"], "requires_reboot": false, "pi_only": false}, {"id": "color-emoji-font", "name": "Color Emoji Font", "description": "Install color emoji fonts for better emoji support in applications", "version": "latest", "category": "appearance", "icon": "emoji_emotions", "size": "~10MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y fonts-noto-color-emoji", "sudo fc-cache -fv"], "remove_commands": ["sudo apt remove -y fonts-noto-color-emoji", "sudo fc-cache -fv"], "check_installed": "fc-list | grep -i emoji", "tags": ["font", "emoji", "unicode"], "requires_reboot": false, "pi_only": false}, {"id": "conky", "name": "<PERSON><PERSON>", "description": "Lightweight system monitor for X that displays system information on desktop", "version": "latest", "category": "appearance", "icon": "monitor", "size": "~5MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y conky-all"], "remove_commands": ["sudo apt remove -y conky-all"], "check_installed": "which conky", "check_version": "conky --version | head -1", "update_commands": ["sudo apt update", "sudo apt upgrade -y conky-all"], "tags": ["system monitor", "desktop", "widgets"], "requires_reboot": false, "pi_only": false}, {"id": "gimp", "name": "GIMP", "description": "GNU Image Manipulation Program - powerful image editor", "version": "latest", "category": "creative", "icon": "image", "size": "~200MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y gimp"], "remove_commands": ["sudo apt remove -y gimp"], "check_installed": "which gimp", "check_version": "gimp --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y gimp"], "tags": ["graphics", "image editing", "design"], "requires_reboot": false, "pi_only": false}, {"id": "kicad", "name": "KiCad", "description": "Open source electronics design automation suite for PCB design", "version": "latest", "category": "engineering", "icon": "electrical_services", "size": "~500MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y kicad"], "remove_commands": ["sudo apt remove -y kicad"], "check_installed": "which kicad", "check_version": "kicad --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y kicad"], "tags": ["pcb", "electronics", "cad"], "requires_reboot": false, "pi_only": false}, {"id": "minecraft-java-prism", "name": "Minecraft Java Prism Launcher", "description": "Advanced Minecraft launcher with mod support and multiple instances", "version": "latest", "category": "games", "icon": "games", "size": "~50MB", "install_type": "script", "install_commands": ["wget -O /tmp/PrismLauncher.AppImage https://github.com/PrismLauncher/PrismLauncher/releases/latest/download/PrismLauncher-Linux-x86_64.AppImage", "chmod +x /tmp/PrismLauncher.AppImage", "sudo mv /tmp/PrismLauncher.AppImage /usr/local/bin/prismlauncher", "sudo apt install -y fuse"], "remove_commands": ["sudo rm -f /usr/local/bin/prismlauncher"], "check_installed": "test -f /usr/local/bin/prismlauncher", "tags": ["minecraft", "gaming", "launcher"], "requires_reboot": false, "pi_only": false}, {"id": "firefox", "name": "Firefox Rapid Release", "description": "Fast, private and secure web browser from Mozilla", "version": "latest", "category": "browsers", "icon": "web", "size": "~100MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y firefox-esr"], "remove_commands": ["sudo apt remove -y firefox-esr"], "check_installed": "which firefox", "check_version": "firefox --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y firefox-esr"], "tags": ["browser", "web", "mozilla"], "requires_reboot": false, "pi_only": false}, {"id": "discord", "name": "Legcord", "description": "Lightweight Discord client with better privacy and customization", "version": "latest", "category": "communication", "icon": "chat", "size": "~80MB", "install_type": "script", "install_commands": ["wget -O /tmp/legcord.deb https://github.com/Legcord/Legcord/releases/latest/download/legcord_*_arm64.deb", "sudo dpkg -i /tmp/legcord.deb", "sudo apt install -f -y", "rm /tmp/legcord.deb"], "remove_commands": ["sudo apt remove -y legcord"], "check_installed": "which legcord", "tags": ["discord", "chat", "communication"], "requires_reboot": false, "pi_only": false}, {"id": "obs-studio", "name": "OBS Studio", "description": "Free and open source software for video recording and live streaming", "version": "latest", "category": "multimedia", "icon": "videocam", "size": "~150MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y obs-studio"], "remove_commands": ["sudo apt remove -y obs-studio"], "check_installed": "which obs", "check_version": "obs --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y obs-studio"], "tags": ["streaming", "recording", "video"], "requires_reboot": false, "pi_only": false}, {"id": "libreoffice", "name": "LibreOffice", "description": "Free and open source office suite with word processor, spreadsheet, and presentation tools", "version": "latest", "category": "office", "icon": "description", "size": "~400MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y libreoffice"], "remove_commands": ["sudo apt remove -y libreoffice*"], "check_installed": "which libreoffice", "check_version": "libreoffice --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y libreoffice"], "tags": ["office", "documents", "productivity"], "requires_reboot": false, "pi_only": false}, {"id": "vscode", "name": "Visual Studio Code", "description": "Lightweight but powerful source code editor with IntelliSense and debugging", "version": "latest", "category": "programming", "icon": "code", "size": "~200MB", "install_type": "script", "install_commands": ["wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg", "sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/", "echo 'deb [arch=arm64,armhf,amd64 signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main' | sudo tee /etc/apt/sources.list.d/vscode.list", "sudo apt update", "sudo apt install -y code"], "remove_commands": ["sudo apt remove -y code", "sudo rm -f /etc/apt/sources.list.d/vscode.list", "sudo rm -f /etc/apt/trusted.gpg.d/packages.microsoft.gpg"], "check_installed": "which code", "check_version": "code --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y code"], "tags": ["ide", "editor", "development"], "requires_reboot": false, "pi_only": false}, {"id": "htop", "name": "htop", "description": "Interactive process viewer and system monitor", "version": "latest", "category": "system", "icon": "monitor_heart", "size": "~1MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y htop"], "remove_commands": ["sudo apt remove -y htop"], "check_installed": "which htop", "check_version": "htop --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y htop"], "tags": ["monitoring", "system", "processes"], "requires_reboot": false, "pi_only": false}, {"id": "alacritty", "name": "Alacritty Terminal", "description": "Fast, cross-platform, OpenGL terminal emulator", "version": "latest", "category": "terminals", "icon": "terminal", "size": "~10MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y alacritty"], "remove_commands": ["sudo apt remove -y alacritty"], "check_installed": "which alacritty", "check_version": "alacritty --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y alacritty"], "tags": ["terminal", "emulator", "fast"], "requires_reboot": false, "pi_only": false}, {"id": "nodejs", "name": "Node.js", "description": "JavaScript runtime built on Chrome's V8 JavaScript engine", "version": "20.x", "category": "tools", "icon": "javascript", "size": "~50MB", "install_type": "script", "install_commands": ["curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -", "sudo apt install -y nodejs"], "remove_commands": ["sudo apt remove -y nodejs npm", "sudo rm -f /etc/apt/sources.list.d/nodesource.list"], "check_installed": "node --version", "check_version": "node --version", "update_commands": ["curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -", "sudo apt update", "sudo apt upgrade -y nodejs"], "tags": ["javascript", "development", "runtime"], "requires_reboot": false, "pi_only": false}, {"id": "docker", "name": "<PERSON>er", "description": "Platform for developing, shipping, and running applications in containers", "version": "latest", "category": "emulation", "icon": "developer_board", "size": "~200MB", "install_type": "script", "install_commands": ["curl -fsSL https://get.docker.com -o get-docker.sh", "sudo sh get-docker.sh", "sudo usermod -aG docker $USER", "rm get-docker.sh"], "remove_commands": ["sudo apt remove -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin", "sudo rm -rf /var/lib/docker", "sudo rm -rf /var/lib/containerd"], "check_installed": "docker --version", "check_version": "docker --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y docker-ce docker-ce-cli containerd.io"], "tags": ["containers", "virtualization", "development"], "requires_reboot": false, "pi_only": false}]}