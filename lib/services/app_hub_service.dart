import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/app_state.dart';
import '../services/rpi_command_service.dart';

class AppHubService {
  final AppState appState;
  final RpiCommandService commandService;

  AppHubRepository? _repository;
  final Map<String, AppInstallationStatus> _installationStatus = {};

  AppHubService(this.appState, this.commandService);

  AppHubRepository? get repository => _repository;
  Map<String, AppInstallationStatus> get installationStatus =>
      _installationStatus;

  /// Load the app repository from assets
  Future<void> loadRepository() async {
    try {
      final jsonString =
          await rootBundle.loadString('assets/app_hub/repository.json');
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      _repository = AppHubRepository.fromJson(jsonData);
    } catch (e) {
      debugPrint('Error loading app repository: $e');
      rethrow;
    }
  }

  /// Check installation status for all apps
  Future<void> checkInstallationStatus(String deviceId) async {
    if (_repository == null) return;

    _installationStatus.clear();

    for (final app in _repository!.applications) {
      _installationStatus[app.id] = AppInstallationStatus.checking;

      try {
        final result = await commandService.executeCommand(
          deviceId,
          app.checkInstalled,
          showProgress: false,
        );

        _installationStatus[app.id] = result.success
            ? AppInstallationStatus.installed
            : AppInstallationStatus.notInstalled;
      } catch (e) {
        _installationStatus[app.id] = AppInstallationStatus.unknown;
      }
    }
  }

  /// Install an application
  Future<bool> installApplication(
      String deviceId, AppHubApplication app) async {
    _installationStatus[app.id] = AppInstallationStatus.installing;

    try {
      // Check dependencies first
      if (app.dependencies.isNotEmpty) {
        for (final dep in app.dependencies) {
          final depApp = _repository?.applications.firstWhere(
            (a) => a.id == dep,
            orElse: () => throw Exception('Dependency $dep not found'),
          );

          if (depApp != null) {
            final depStatus = _installationStatus[dep];
            if (depStatus != AppInstallationStatus.installed) {
              // Install dependency first
              final depInstalled = await installApplication(deviceId, depApp);
              if (!depInstalled) {
                throw Exception('Failed to install dependency: $dep');
              }
            }
          }
        }
      }

      // Execute installation commands
      for (final command in app.installCommands) {
        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Installing ${app.name}',
          timeout: const Duration(minutes: 30),
        );

        if (!result.success) {
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        }
      }

      // Verify installation
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (verifyResult.success) {
        _installationStatus[app.id] = AppInstallationStatus.installed;
        return true;
      } else {
        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      debugPrint('Error installing ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    }
  }

  /// Remove an application
  Future<bool> removeApplication(String deviceId, AppHubApplication app) async {
    _installationStatus[app.id] = AppInstallationStatus.removing;

    try {
      // Execute removal commands
      for (final command in app.removeCommands) {
        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Removing ${app.name}',
          timeout: const Duration(minutes: 15),
        );

        if (!result.success) {
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        }
      }

      // Verify removal
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (!verifyResult.success) {
        _installationStatus[app.id] = AppInstallationStatus.notInstalled;
        return true;
      } else {
        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      debugPrint('Error removing ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    }
  }

  /// Get applications by category
  List<AppHubApplication> getApplicationsByCategory(String categoryId) {
    if (_repository == null) return [];
    return _repository!.applications
        .where((app) => app.category == categoryId)
        .toList();
  }

  /// Search applications
  List<AppHubApplication> searchApplications(String query) {
    if (_repository == null) return [];
    final lowerQuery = query.toLowerCase();
    return _repository!.applications.where((app) {
      return app.name.toLowerCase().contains(lowerQuery) ||
          app.description.toLowerCase().contains(lowerQuery) ||
          app.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }
}

enum AppInstallationStatus {
  unknown,
  checking,
  notInstalled,
  installed,
  installing,
  removing,
  failed,
}

class AppHubRepository {
  final RepositoryInfo repository;
  final List<AppCategory> categories;
  final List<AppHubApplication> applications;

  AppHubRepository({
    required this.repository,
    required this.categories,
    required this.applications,
  });

  factory AppHubRepository.fromJson(Map<String, dynamic> json) {
    return AppHubRepository(
      repository: RepositoryInfo.fromJson(json['repository']),
      categories: (json['categories'] as List)
          .map((cat) => AppCategory.fromJson(cat))
          .toList(),
      applications: (json['applications'] as List)
          .map((app) => AppHubApplication.fromJson(app))
          .toList(),
    );
  }
}

class RepositoryInfo {
  final String name;
  final String version;
  final String description;
  final DateTime lastUpdated;

  RepositoryInfo({
    required this.name,
    required this.version,
    required this.description,
    required this.lastUpdated,
  });

  factory RepositoryInfo.fromJson(Map<String, dynamic> json) {
    return RepositoryInfo(
      name: json['name'],
      version: json['version'],
      description: json['description'],
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }
}

class AppCategory {
  final String id;
  final String name;
  final String description;
  final String icon;

  AppCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
  });

  factory AppCategory.fromJson(Map<String, dynamic> json) {
    return AppCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
    );
  }
}

class AppHubApplication {
  final String id;
  final String name;
  final String description;
  final String version;
  final String category;
  final String icon;
  final String size;
  final String installType;
  final List<String> installCommands;
  final List<String> removeCommands;
  final String checkInstalled;
  final List<String> tags;
  final bool requiresReboot;
  final bool piOnly;
  final List<String> dependencies;

  AppHubApplication({
    required this.id,
    required this.name,
    required this.description,
    required this.version,
    required this.category,
    required this.icon,
    required this.size,
    required this.installType,
    required this.installCommands,
    required this.removeCommands,
    required this.checkInstalled,
    required this.tags,
    required this.requiresReboot,
    required this.piOnly,
    this.dependencies = const [],
  });

  factory AppHubApplication.fromJson(Map<String, dynamic> json) {
    return AppHubApplication(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      version: json['version'],
      category: json['category'],
      icon: json['icon'],
      size: json['size'],
      installType: json['install_type'],
      installCommands: List<String>.from(json['install_commands']),
      removeCommands: List<String>.from(json['remove_commands']),
      checkInstalled: json['check_installed'],
      tags: List<String>.from(json['tags']),
      requiresReboot: json['requires_reboot'] ?? false,
      piOnly: json['pi_only'] ?? false,
      dependencies: List<String>.from(json['dependencies'] ?? []),
    );
  }
}
