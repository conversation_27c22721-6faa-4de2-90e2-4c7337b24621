name: jelly_pi
description: "Jelly Pi - A cross-platform network device manager for Raspberry Pi and SSH-enabled devices."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # Secure storage for sensitive data
  dartssh2: ^2.11.0
  clipboard: ^0.1.3
  http: ^1.3.0
  cupertino_icons: ^1.0.8
  provider: ^6.1.1
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2
  xterm: ^4.0.0
  fluent_ui: ^4.10.0
  flutter_keyboard_visibility: ^6.0.0
  flutter_platform_widgets: ^7.0.1
  material_color_utilities: ^0.11.1
  flutter_highlight: ^0.7.0
  context_menus: ^2.0.0+1
  ansi_up: ^2.1.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_reorderable_grid_view: ^5.5.0
  file_picker: ^8.1.2
  path: ^1.9.0
  archive: ^3.4.10
  google_fonts: ^6.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/apps/
    - assets/apps/manifest.json
    - assets/apps/system_monitor/app.json
    - assets/apps/system_monitor/config.json
    - assets/apps/system_monitor/README.md
    - assets/apps/file_manager/app.json
    - assets/apps/file_manager/config.json
    - assets/apps/network_tools/app.json
    - assets/apps/network_tools/config.json
    - assets/apps/network_tools/README.md
    - assets/apps/rpi_management/app.json
    - assets/apps/rpi_management/config.json
    - assets/apps/rpi_management/README.md
    - assets/apps/camera_stream/app.json
    - assets/apps/camera_stream/config.json
    - assets/apps/camera_stream/README.md
    - assets/apps/gpio_control/app.json
    - assets/apps/gpio_control/config.json
    - assets/apps/gpio_control/README.md
    - assets/apps/log_viewer/app.json
    - assets/apps/log_viewer/config.json
    - assets/apps/log_viewer/README.md
    - assets/apps/performance_monitor/app.json
    - assets/apps/performance_monitor/config.json
    - assets/apps/performance_monitor/README.md
    - assets/apps/ai_assistant/app.json
    - assets/apps/ai_assistant/config.json
    - assets/apps/ai_assistant/README.md
    - lib/scripts/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
